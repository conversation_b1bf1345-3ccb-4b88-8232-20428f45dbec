import { IsArray, IsString, Length, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class ListFileNamesDto {
  @IsString()
  name: string;

  @IsString()
  position: string;
}

export class AdditionalInformationExistingEcoCuteInfoDto {
  @IsString()
  @Length(64)
  verificationToken: string;

  @ValidateNested()
  @Type(() => ListFileNamesDto)
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return [];
    }
  })
  @IsArray()
  listFileNames: ListFileNamesDto[];
}
