export enum AdditionalInfoStatus {
  NOT_UPLOADED = 1,
  AWAITING_REUPLOAD = 2,
  UPLOADED = 3,
}

export enum AdditionActivityType {
  CREATE = 1,
  UPLOAD = 2,
  REACTIVE = 3,
  DELETE = 4,
}

export const AdditionalInfoStatusMapping = {
  [AdditionalInfoStatus.NOT_UPLOADED]: '未アップロード',
  [AdditionalInfoStatus.AWAITING_REUPLOAD]: '再アップロード待ち',
  [AdditionalInfoStatus.UPLOADED]: 'アップロード済み',
};

export enum AdditionalInfoTemplate {
  HotWaterEnergySaving2025 = 1,
  TokyoZeroEmissionPoint = 2,
  CountertopDishwasher = 3,
  ExistingEcoCuteInfoForm = 5,
}

export const AdditionalInfoTemplateMapping = {
  [AdditionalInfoTemplate.HotWaterEnergySaving2025]: '給湯省エネ2025事業',
  [AdditionalInfoTemplate.TokyoZeroEmissionPoint]: '東京ゼロエミポイント',
  [AdditionalInfoTemplate.CountertopDishwasher]: '卓上食器洗い食洗機の設置工事用',
};

export const AdditionalInformationAccountType = ['普通預金', '当座預金'];
